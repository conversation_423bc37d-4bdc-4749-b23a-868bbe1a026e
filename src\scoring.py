# src/scoring.py

import pandas as pd
import numpy as np
from itertools import combinations
from typing import Dict, <PERSON><PERSON>, Tu<PERSON>, List
import logging

def compare_asset_pair(
    asset_a_signal: pd.Series,
    asset_b_signal: pd.Series
) -> pd.Series:
    """
    Compare two assets directly and return a binary series indicating
    whether asset A outperforms asset B at each timestamp.

    This mimics how PineScript compares assets using request.security with pairs.

    Args:
        asset_a_signal: Signal series for asset A (0 or 1)
        asset_b_signal: Signal series for asset B (0 or 1)

    Returns:
        Binary series where 1 means asset A outperforms B, 0 means it doesn't
    """
    # Ensure both series have the same index
    common_idx = asset_a_signal.index.intersection(asset_b_signal.index)
    if len(common_idx) == 0:
        return pd.Series(0, index=asset_a_signal.index)

    asset_a_signal = asset_a_signal.loc[common_idx]
    asset_b_signal = asset_b_signal.loc[common_idx]

    # In PineScript, one asset outperforms another if its signal is 1 while
    # the other's is 0. If both are 1 or both are 0, neither outperforms.
    # This mimics the behavior in f_trend and f_oppose functions in PineScript
    comparison = (asset_a_signal == 1) & (asset_b_signal == 0)
    return comparison.astype(int)

def calculate_binary_comparison(asset_a_signal: float, asset_b_signal: float) -> int:
    """
    Calculates a binary comparison result between two asset signals.

    Args:
        asset_a_signal: Signal value for asset A (typically 0 or 1)
        asset_b_signal: Signal value for asset B (typically 0 or 1)

    Returns:
        1 if asset A's signal is 1 and asset B's is 0, otherwise 0
    """
    # Convert signals to float to ensure consistent comparison
    asset_a_signal = float(asset_a_signal)
    asset_b_signal = float(asset_b_signal)

    # Debug output to understand comparison logic
    result = 1 if (asset_a_signal == 1.0 and asset_b_signal == 0.0) else 0
    logging.info(f"Compare: A={asset_a_signal} (type: {type(asset_a_signal)}) vs B={asset_b_signal} (type: {type(asset_b_signal)}) => Result={result}")
    return result

def calculate_asset_score(asset_symbol: str, signals_dict: Dict[str, float]) -> int:
    """
    Calculates the score for a single asset by summing up binary comparisons against all other assets.

    This directly mimics the PineScript logic:
    series int a1_score = c1_2 + c1_3 + c1_4 + c1_5 + c1_6 + c1_7 + c1_usd

    Args:
        asset_symbol: The symbol of the asset being scored
        signals_dict: Dictionary mapping asset symbols to their signals

    Returns:
        The total score for the asset
    """
    score = 0
    asset_signal = signals_dict.get(asset_symbol, 0.0)

    for other_asset, other_signal in signals_dict.items():
        if other_asset == asset_symbol:
            continue  # Skip self-comparison

        # Add 1 point if this asset's signal beats the other asset's signal
        # Asset A beats Asset B if A's signal is 1 and B's signal is 0
        score += calculate_binary_comparison(asset_signal, other_signal)

    return score

def calculate_scores_for_timestamp(
    pgo_signals_at_timestamp: pd.Series
) -> Dict[str, int]:
    """
    Calculates scores for assets based on pairwise comparison of their PGO
    signals at a specific timestamp, exactly matching the PineScript implementation.

    Args:
        pgo_signals_at_timestamp: A Pandas Series where the index contains
                                  asset symbols (str) and the values are their
                                  PGO signals (e.g., 1.0 or 0.0) for the
                                  specific timestamp being evaluated.

    Returns:
        scores (Dict[str, int]): A dictionary where keys are asset symbols
                                 and values are their calculated scores for
                                 that timestamp.
    """
    if not isinstance(pgo_signals_at_timestamp, pd.Series):
        raise TypeError("Input 'pgo_signals_at_timestamp' must be a Pandas Series.")

    # Ensure signals are numeric and handle NaNs (treat as 0)
    signals = pgo_signals_at_timestamp.fillna(0.0).astype(float)

    logging.info(f"Calculating scores for timestamp with signals: {signals}")

    # Get the list of actual cryptocurrency assets (excluding USD if present)
    asset_symbols = list(signals.index)
    logging.warning(f"[DEBUG] SCORES - Asset symbols for scoring: {asset_symbols}")
    logging.warning(f"[DEBUG] SCORES - Asset symbols order: {asset_symbols}")

    # Create a signals dictionary (without USD)
    signals_dict = signals.to_dict()
    logging.warning(f"[DEBUG] SCORES - Signals dict keys order: {list(signals_dict.keys())}")

    # Calculate scores for each asset (without USD)
    scores = {}
    for asset in asset_symbols:
        # Calculate the base score without any tiebreaker
        base_score = calculate_asset_score(asset, signals_dict)
        scores[asset] = base_score

        # Log the score for debugging
        logging.warning(f"[DEBUG] SCORES - Asset {asset}: Score {scores[asset]}")

    logging.error(f"[DEBUG] FINAL SCORES DICTIONARY: {scores}")
    logging.error(f"[DEBUG] FINAL SCORES KEYS ORDER: {list(scores.keys())}")
    return scores

def find_best_asset_for_day(scores: Dict[str, int], mtpi_signal: Optional[int] = None) -> str:
    """
    Determines the best asset based on scores for a single day.

    Args:
        scores: A dictionary {asset_symbol: score} for the day.
        mtpi_signal: Optional MTPI signal value. If provided and not 1 (bullish),
                    returns an empty string to indicate no asset should be selected.

    Returns:
        The symbol of the asset with the highest score, or empty string if
        mtpi_signal is provided and not bullish (1).

        Note: Based on the scoring mechanism, there should never be tied scores.
        If multiple assets have the same maximum score, a warning will be logged
        and the first asset found with the maximum score will be returned.
    """
    logging.warning(f"[DEBUG] ASSET SELECTION - find_best_asset_for_day() called with {len(scores)} assets")
    logging.warning(f"[DEBUG] ASSET SELECTION - Input scores: {scores}")
    logging.warning(f"[DEBUG] ASSET SELECTION - Dictionary keys order: {list(scores.keys())}")

    if not scores:
        logging.warning("No scores provided, returning empty string")
        return ''  # Return empty string if no scores

    # Check if MTPI signal is provided and not bullish
    if mtpi_signal is not None and mtpi_signal != 1:
        logging.info(f"MTPI signal is {mtpi_signal}, staying out of the market")
        return ''  # Return empty string to indicate no asset should be selected

    # Find the maximum score among all assets
    max_score = max(scores.values())
    logging.warning(f"[DEBUG] ASSET SELECTION - Maximum score found: {max_score}")

    # If all scores are 0 and MTPI signal is not explicitly bullish, stay out of the market
    if max_score == 0 and (mtpi_signal is None or mtpi_signal != 1):
        logging.info("All asset scores are 0, staying out of the market")
        return ''  # Return empty string to indicate no asset should be selected

    # Get all assets with the maximum score
    top_assets = [asset for asset, score in scores.items() if score == max_score]
    logging.warning(f"[DEBUG] ASSET SELECTION - Assets with max score {max_score}: {top_assets}")

    # Enhanced logging for tie-breaking analysis
    if len(top_assets) > 1:
        logging.error(f"[DEBUG] TIE DETECTED: {len(top_assets)} assets have the same maximum score of {max_score}")
        logging.error(f"[DEBUG] Tied assets: {top_assets}")
        logging.error(f"[DEBUG] Dictionary iteration order: {list(scores.keys())}")

        # DETERMINISTIC TIE-BREAKING: Use the order from the scores dictionary
        # The scores dictionary preserves the order from the original asset configuration
        # So the first asset in the dictionary order should be selected
        best_asset = top_assets[0]
        logging.error(f"[DEBUG] Tie-breaking: Selecting first asset in dictionary order: {best_asset}")

        # Log the complete ranking for debugging
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        logging.error("[DEBUG] Complete asset ranking:")
        for i, (asset, score) in enumerate(sorted_scores, 1):
            tie_indicator = " (TIED)" if score == max_score else ""
            selected_indicator = " <- SELECTED" if asset == best_asset else ""
            logging.error(f"[DEBUG]   {i}. {asset}: {score}{tie_indicator}{selected_indicator}")
    else:
        logging.warning(f"[DEBUG] ASSET SELECTION - No tie detected, single winner: {top_assets[0]}")
        best_asset = top_assets[0]

    logging.error(f"[DEBUG] SELECTED BEST ASSET: {best_asset} (score: {scores[best_asset]})")

    return best_asset

def calculate_pairwise_win_matrix(daily_scores: pd.DataFrame) -> Optional[pd.DataFrame]:
    """
    Calculates a matrix showing the percentage of time each asset scored
    strictly higher than another asset over the period covered by daily_scores.

    Args:
        daily_scores: DataFrame of daily scores, indexed by timestamp, with
                      columns for each asset symbol. Assumes scores are integers.

    Returns:
        pd.DataFrame: A square DataFrame where index and columns are asset symbols.
                      The value at matrix[row_asset, col_asset] represents the
                      percentage of days where row_asset's score was strictly
                      greater than col_asset's score. Returns None if daily_scores
                      is empty or has fewer than 2 assets.
    """
    if not isinstance(daily_scores, pd.DataFrame):
        raise TypeError("daily_scores must be a Pandas DataFrame.")
    if daily_scores.empty:
        print("Warning: calculate_pairwise_win_matrix received empty daily_scores DataFrame.")
        return None

    asset_symbols = daily_scores.columns.tolist()
    if len(asset_symbols) < 2:
        print("Warning: calculate_pairwise_win_matrix requires at least 2 assets.")
        return None

    num_days = len(daily_scores)
    if num_days == 0:
         print("Warning: calculate_pairwise_win_matrix received daily_scores with 0 days.")
         return pd.DataFrame(0.0, index=asset_symbols, columns=asset_symbols) # Return matrix of zeros

    # Initialize win count matrix
    win_counts = pd.DataFrame(0.0, index=asset_symbols, columns=asset_symbols)

    # Iterate through all pairs of assets
    for asset_a in asset_symbols:
        for asset_b in asset_symbols:
            if asset_a == asset_b:
                continue # Skip self-comparison

            # Count days where asset_a score > asset_b score
            wins = (daily_scores[asset_a] > daily_scores[asset_b]).sum()
            win_counts.loc[asset_a, asset_b] = wins

    # Convert counts to percentages
    win_matrix_pct = (win_counts / num_days) #* 100.0 # Keep as fraction for easier formatting later

    return win_matrix_pct