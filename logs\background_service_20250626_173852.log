2025-06-26 17:38:53,023 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:38:53,838 - root - INFO - Loaded 49 trade records from logs/trades\trade_log_********.json
2025-06-26 17:38:53,839 - root - INFO - Loaded 29 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:38:53,839 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:38:54,731 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:54,743 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:54,751 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:54,757 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:54,758 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:54,761 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:54,761 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:38:54,761 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:38:55,573 - root - INFO - Telegram command handlers registered
2025-06-26 17:38:55,573 - root - INFO - Telegram bot polling started
2025-06-26 17:38:55,573 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:38:55,573 - root - INFO - Telegram notification channel initialized
2025-06-26 17:38:55,573 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:38:55,573 - root - INFO - Loaded 24 templates from file
2025-06-26 17:38:55,573 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:38:55,573 - root - INFO - Notification manager initialized
2025-06-26 17:38:55,573 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:38:55,573 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:38:55,573 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:38:55,573 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:38:55,573 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:38:55,581 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:38:55,583 - root - INFO - Recovery manager initialized
2025-06-26 17:38:55,583 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 17:38:55,583 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 17:38:55,583 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:55,592 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:55,592 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:38:55,592 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:38:55,592 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:38:55,594 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:38:55,594 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:38:55,594 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:38:55,594 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:38:55,594 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:38:55,594 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:55,601 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:55,635 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:38:55,651 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:38:55,653 - telegram.ext.Application - INFO - Application started
2025-06-26 17:38:55,829 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:38:55,829 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:38:55,829 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:38:55,829 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:38:55,829 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:38:55,829 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:55,836 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:55,839 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:55,845 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:55,846 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:38:55,851 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:55,851 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:38:55,852 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:38:55,852 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:38:55,852 - root - INFO - Trading mode: paper
2025-06-26 17:38:55,853 - root - INFO - Trading enabled: True
2025-06-26 17:38:55,853 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:38:55,854 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:38:55,854 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:38:55,854 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:38:55,854 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:38:55,861 - root - INFO - Configuration loaded successfully.
2025-06-26 17:38:56,101 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:38:56,101 - root - INFO - Trading enabled in paper mode
2025-06-26 17:38:56,102 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:38:56,102 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:38:56,102 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:38:56,102 - root - INFO - Generated run ID: ********_173856
2025-06-26 17:38:56,102 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:38:56,102 - root - INFO - Background service initialized
2025-06-26 17:38:56,102 - root - INFO - Network watchdog started
2025-06-26 17:38:56,102 - root - INFO - Network watchdog started
2025-06-26 17:38:56,107 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:38:56,107 - root - INFO - Background service started
2025-06-26 17:38:56,107 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:38:56,108 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:38:56,108 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:38:56,108 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:38:56,108 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:38:56,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:39:02,116 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:39:02,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:39:02,176 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:39:02,176 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:39:02,177 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:39:02,178 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:39:02,206 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,207 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,207 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,208 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:39:02,209 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,227 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,228 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,228 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,228 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 17:39:02,230 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,245 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,245 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,245 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,246 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:39:02,246 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,257 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,258 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,258 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,258 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:39:02,259 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,271 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,272 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,272 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,272 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:39:02,273 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,286 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,286 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,286 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,287 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:39:02,287 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,302 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,302 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,303 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,303 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:39:02,304 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,324 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,325 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,326 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,326 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:39:02,327 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,344 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,344 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,344 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,344 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:39:02,345 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,351 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,351 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,352 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,352 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:39:02,352 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,368 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,368 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,368 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,369 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:39:02,369 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,385 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,385 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,385 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,386 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:39:02,386 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,398 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,399 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,399 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,399 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:39:02,400 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,402 - root - INFO - Using 13 trend assets (USDT) for analysis and 13 trading assets (EUR) for execution
2025-06-26 17:39:02,402 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:39:02,402 - root - INFO -   - Number of indicators: 8
2025-06-26 17:39:02,402 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:39:02,402 - root - INFO -   - Combination method: consensus
2025-06-26 17:39:02,402 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:39:02,403 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:39:02,403 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:39:02,403 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:39:02,403 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-26 17:39:02,403 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: NOT FOUND
2025-06-26 17:39:02,403 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:39:02,403 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:39:02,404 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:39:02,404 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:39:02,404 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:39:02,404 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:39:02,404 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:39:02,404 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:39:02,411 - root - INFO - Configuration loaded successfully.
2025-06-26 17:39:02,412 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:39:02,417 - root - INFO - Configuration saved successfully.
2025-06-26 17:39:02,417 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:39:02,417 - root - INFO - Number of trend detection assets: 13
2025-06-26 17:39:02,417 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:39:02,418 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:39:02,418 - root - INFO - Number of trading assets: 13
2025-06-26 17:39:02,418 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:39:02,588 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:39:02,596 - root - INFO - Configuration loaded successfully.
2025-06-26 17:39:02,607 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:39:02,615 - root - INFO - Configuration loaded successfully.
2025-06-26 17:39:02,615 - root - INFO - Execution context: backtesting
2025-06-26 17:39:02,615 - root - INFO - Execution timing: candle_close
2025-06-26 17:39:02,616 - root - INFO - Ratio calculation method: independent
2025-06-26 17:39:02,616 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:39:02,616 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:39:02,616 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:39:02,617 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:39:02,617 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:39:02,618 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:39:02,618 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:39:02,619 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,619 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,620 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,620 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,621 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,622 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,623 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,623 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,623 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,625 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,625 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,626 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,626 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:39:02,626 - root - INFO - Checking cache for 13 symbols (1d)...
2025-06-26 17:39:02,646 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,648 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,649 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,649 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:39:02,669 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,671 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,671 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,671 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:39:02,688 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,689 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,690 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,690 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:39:02,698 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,701 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,702 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,702 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:39:02,719 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,722 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,723 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,723 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:39:02,736 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,737 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,738 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,738 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:39:02,752 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,753 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,754 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,754 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:39:02,769 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,771 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,771 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,771 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:39:02,786 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,787 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,787 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,788 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:39:02,794 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,794 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,795 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,795 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:39:02,808 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,809 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,809 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,810 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:39:02,821 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,823 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,823 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,823 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:39:02,841 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,842 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:39:02,843 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,843 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:39:02,843 - root - INFO - All 13 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:39:02,843 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,844 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,844 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,844 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,844 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,844 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,845 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,845 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,845 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,845 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,845 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,845 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,846 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:39:02,858 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:39:02,858 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:39:02,859 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:39:02,859 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:39:02,859 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:39:02,865 - root - INFO - Configuration loaded successfully.
2025-06-26 17:39:02,865 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:39:02,866 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:39:02,866 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:39:02,866 - root - INFO - Override: combination_method = consensus
2025-06-26 17:39:02,866 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:39:02,866 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:39:02,867 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:39:02,867 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:39:02,867 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:39:02,868 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:39:02,880 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:39:02,880 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:39:02,882 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:39:02,882 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:39:02,882 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:02,882 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:39:02,906 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:39:02,906 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:39:02,906 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:39:02,907 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:39:02,915 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:39:02,915 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:39:02,915 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:39:03,232 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:39:03,232 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:39:03,268 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:39:03,269 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:39:03,269 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:39:03,270 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:39:03,336 - root - INFO - Generated DPSD signals
2025-06-26 17:39:03,337 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:39:03,337 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:39:03,337 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:39:03,345 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:39:03,345 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:39:03,346 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:39:03,388 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:39:03,388 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:39:03,389 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:39:03,459 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:39:03,466 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:39:03,467 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:39:03,467 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:39:03,467 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:39:03,468 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:39:03,469 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:39:03,475 - root - INFO - Configuration saved successfully.
2025-06-26 17:39:03,476 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:39:03,476 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:39:03,484 - root - INFO - Configuration loaded successfully.
2025-06-26 17:39:03,484 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:39:03,484 - root - INFO - Calculating ratio PGO signals for 13 assets with PGO(35) using full OHLCV data
2025-06-26 17:39:03,485 - root - INFO - Using ratio calculation method: independent
2025-06-26 17:39:03,501 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,525 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:03,551 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:03,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,570 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:03,576 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:03,599 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:39:03,599 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,622 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:39:03,628 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:03,651 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:03,651 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,666 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:03,672 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:03,688 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:03,688 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,702 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:03,708 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:03,723 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:39:03,723 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,736 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:39:03,741 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:03,755 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:03,756 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,770 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:03,775 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:03,790 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:39:03,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,807 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:39:03,810 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:03,831 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:03,831 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,850 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:03,857 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:03,873 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:03,873 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,889 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:03,890 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:03,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,933 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:03,955 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:39:03,955 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:03,971 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:39:03,973 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:03,991 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:03,991 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,007 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:04,011 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:04,023 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,041 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:04,061 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:04,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,074 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:04,081 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:04,096 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:04,096 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,107 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:04,111 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:04,131 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:04,131 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,146 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:04,151 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:04,166 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:39:04,170 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,183 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:39:04,188 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:04,205 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:04,205 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,234 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:04,245 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:04,278 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:04,281 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,291 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:04,299 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:04,311 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:04,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,324 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:04,333 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:04,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,366 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:04,381 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,401 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:04,416 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:39:04,416 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,431 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:39:04,433 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:04,451 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:39:04,451 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,461 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:39:04,466 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:04,483 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,501 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:04,511 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:04,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,531 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:04,536 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:04,552 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:04,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,561 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:04,566 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:04,585 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:39:04,585 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,600 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:39:04,605 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:04,616 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:39:04,620 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,633 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:39:04,638 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:04,651 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:04,651 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,666 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:04,673 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:04,683 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:04,683 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,701 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:04,706 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:04,723 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:39:04,723 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,741 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:39:04,741 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:04,761 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:04,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,773 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:04,781 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:04,796 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:04,796 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,811 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:04,817 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:04,831 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:04,831 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,841 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:04,853 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:04,861 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:04,861 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,883 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:04,888 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:04,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,923 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:04,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,956 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:04,971 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:04,993 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:05,011 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:39:05,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,023 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:39:05,033 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:05,051 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,073 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:05,091 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,111 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:05,123 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,123 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,141 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,141 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:05,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,183 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:05,190 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:05,190 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,211 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:05,220 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:05,233 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,256 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:05,266 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,288 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:05,301 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:39:05,301 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,311 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:39:05,323 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:05,338 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,356 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:05,373 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,391 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:05,407 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:05,408 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,421 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:05,423 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:05,441 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,441 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,450 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,456 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:05,473 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:39:05,473 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,483 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:39:05,490 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:05,506 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:39:05,506 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,517 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:39:05,523 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:05,538 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,539 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,550 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,556 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:05,573 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,573 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,583 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:39:05,591 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:05,607 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,623 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:05,638 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,656 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:05,667 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:39:05,667 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,683 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:39:05,689 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:05,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:39:05,706 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,723 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:05,739 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,757 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:05,772 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,791 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:05,806 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,823 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:05,838 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,857 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:05,861 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:05,861 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,883 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:05,890 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:05,901 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:05,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,911 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:05,922 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:05,933 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:05,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,951 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:05,956 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:05,966 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:05,988 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:06,001 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:39:06,001 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,011 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:39:06,022 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:06,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,061 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:06,078 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:06,078 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,093 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:06,097 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:06,111 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,133 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:06,151 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,174 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:06,200 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,224 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:06,243 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,266 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:06,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,306 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:06,325 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:39:06,325 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,345 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:39:06,353 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:06,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,400 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:06,418 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:39:06,418 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,435 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:39:06,445 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:06,474 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,497 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:06,510 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,537 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:06,556 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,577 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:06,596 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,611 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:06,640 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:06,640 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,657 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:06,661 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:06,684 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,706 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:06,725 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,745 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:06,761 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:39:06,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,784 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:39:06,790 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:06,807 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,831 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:06,851 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:39:06,851 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,861 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:39:06,871 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:06,888 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:06,888 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,901 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:06,907 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:06,923 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:39:06,926 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,941 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:39:06,941 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:06,961 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:06,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:06,985 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:06,991 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:07,011 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:07,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,023 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:07,033 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:07,050 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,073 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:07,089 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:07,090 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,105 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:07,110 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:07,123 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,145 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:07,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,192 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:07,220 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,241 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:07,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,291 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:07,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,339 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:07,361 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:39:07,361 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,380 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:39:07,386 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:07,407 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,435 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:07,452 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,473 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:07,489 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:07,489 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,500 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:07,506 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:07,523 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,540 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:07,565 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,583 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:07,596 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,610 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:07,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,650 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:07,661 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,684 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:07,699 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,716 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:07,730 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,751 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:07,766 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,783 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:07,800 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:39:07,800 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,811 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:39:07,811 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:07,833 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:07,833 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,846 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:39:07,851 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:07,866 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:07,866 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,873 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:39:07,883 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:07,901 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:39:07,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,910 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:39:07,910 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:07,931 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,951 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:07,966 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:07,983 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:07,996 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,017 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:08,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,051 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:08,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,085 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:08,101 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,117 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:08,133 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:08,133 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,140 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:08,151 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:08,165 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:08,165 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,181 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:39:08,183 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:08,200 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:08,200 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,215 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:08,216 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:08,235 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:39:08,235 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,246 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:39:08,251 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:08,272 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:39:08,272 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,287 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:39:08,293 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:08,309 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:08,309 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,323 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:39:08,325 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:08,341 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:39:08,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,358 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:39:08,361 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:08,381 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,401 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:08,417 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:08,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,431 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:08,433 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:08,451 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,473 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:08,492 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,513 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:08,533 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:08,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,551 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:08,558 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:08,575 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:08,575 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,594 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:08,601 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:08,619 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:08,619 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,634 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:39:08,641 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:08,661 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:39:08,661 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,676 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:39:08,681 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:08,692 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:39:08,692 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,711 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:39:08,716 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:08,734 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:08,734 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,741 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:08,751 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:08,761 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:39:08,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,781 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:39:08,789 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:08,805 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:08,806 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,818 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:08,824 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:08,841 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,857 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:08,875 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,891 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:39:08,907 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,924 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:39:08,939 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,957 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:39:08,972 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:08,990 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:39:09,001 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:09,005 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,016 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:39:09,023 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:39:09,033 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:09,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,051 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:39:09,057 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:39:09,072 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:09,072 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,087 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:39:09,091 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:39:09,109 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:39:09,112 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,123 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:39:09,133 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:39:09,150 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:39:09,150 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,161 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:39:09,167 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:39:09,184 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:39:09,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,191 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:39:09,201 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:39:09,221 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:09,221 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,233 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:39:09,239 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:39:09,250 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,272 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:39:09,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:39:09,305 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:39:10,779 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:39:10,780 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 13)
2025-06-26 17:39:10,780 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:39:10,782 - root - INFO - Date ranges for each asset:
2025-06-26 17:39:10,783 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,783 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,783 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,783 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,784 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,784 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,784 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,784 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,785 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,785 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,785 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,786 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,786 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,786 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:39:10,787 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:39:10,791 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:39:10,791 - root - INFO -    Execution Method: candle_close
2025-06-26 17:39:10,791 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:39:10,791 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:39:10,794 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:39:10,794 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:39:10,794 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:39:10,794 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,794 - root - INFO -    Buying: ['BTC/USDT']
2025-06-26 17:39:10,794 - root - INFO -    BTC/USDT buy price: $95778.2000 (close price)
2025-06-26 17:39:10,794 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,794 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,800 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,801 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,801 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,801 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,801 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,801 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,801 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,801 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:39:10,802 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:39:10,802 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:39:10,802 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,802 - root - INFO -    Selling: ['BTC/USDT']
2025-06-26 17:39:10,802 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:39:10,802 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:39:10,802 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,802 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,805 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,805 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,805 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,805 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,805 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,805 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,806 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,806 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,806 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,806 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,806 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,806 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,806 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:39:10,811 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:39:10,811 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:39:10,811 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:39:10,811 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,811 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:39:10,811 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:39:10,811 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:39:10,811 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:39:10,811 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:39:10,811 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:39:10,811 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,811 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:39:10,811 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:39:10,817 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:39:10,817 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 17:39:10,817 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 17:39:10,817 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 17:39:10,817 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,817 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:39:10,817 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 17:39:10,817 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 17:39:10,817 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 17:39:10,817 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 17:39:10,817 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 17:39:10,817 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,817 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 17:39:10,821 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:39:10,821 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 17:39:10,822 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 17:39:10,822 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 17:39:10,823 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 17:39:10,823 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,823 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:39:10,823 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 17:39:10,823 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 17:39:10,823 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:39:10,823 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:39:10,823 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:39:10,823 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,823 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 17:39:10,823 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:39:10,823 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:39:10,823 - root - INFO - ASSET CHANGE DETECTED on 2025-04-04:
2025-06-26 17:39:10,823 - root - INFO -    Signal Date: 2025-04-03 (generated at 00:00 UTC)
2025-06-26 17:39:10,823 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-04 00:00 UTC (immediate)
2025-06-26 17:39:10,823 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,823 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:39:10,823 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:39:10,823 - root - INFO -    BNB/USDT buy price: $597.7100 (close price)
2025-06-26 17:39:10,823 - root - INFO - ASSET CHANGE DETECTED on 2025-04-09:
2025-06-26 17:39:10,823 - root - INFO -    Signal Date: 2025-04-08 (generated at 00:00 UTC)
2025-06-26 17:39:10,823 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-09 00:00 UTC (immediate)
2025-06-26 17:39:10,823 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,823 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:39:10,823 - root - INFO -    Buying: ['BTC/USDT']
2025-06-26 17:39:10,823 - root - INFO -    BTC/USDT buy price: $82615.2200 (close price)
2025-06-26 17:39:10,831 - root - INFO - ASSET CHANGE DETECTED on 2025-04-17:
2025-06-26 17:39:10,831 - root - INFO -    Signal Date: 2025-04-16 (generated at 00:00 UTC)
2025-06-26 17:39:10,831 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-17 00:00 UTC (immediate)
2025-06-26 17:39:10,833 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,833 - root - INFO -    Selling: ['BTC/USDT']
2025-06-26 17:39:10,833 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:39:10,833 - root - INFO -    SOL/USDT buy price: $134.8300 (close price)
2025-06-26 17:39:10,835 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:39:10,835 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:39:10,835 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:39:10,835 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,835 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:39:10,835 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:39:10,835 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:39:10,841 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:39:10,841 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:39:10,841 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:39:10,841 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,841 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:39:10,841 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:39:10,841 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:39:10,841 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:39:10,841 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:39:10,841 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:39:10,841 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,841 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:39:10,841 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:39:10,841 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:39:10,841 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 17:39:10,841 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 17:39:10,841 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 17:39:10,841 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,841 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:39:10,841 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:39:10,841 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:39:10,841 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 17:39:10,841 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 17:39:10,841 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 17:39:10,841 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,841 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:39:10,841 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:39:10,841 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 17:39:10,855 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:39:10,856 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:39:10,856 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:39:10,856 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:39:10,856 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:39:10,856 - root - INFO -    Buying: ['BTC/USDT']
2025-06-26 17:39:10,857 - root - INFO -    BTC/USDT buy price: $102120.0100 (close price)
2025-06-26 17:39:10,888 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: BTC/USDT
2025-06-26 17:39:10,888 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: BTC/USDT -> BNB/USDT
2025-06-26 17:39:10,889 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: BNB/USDT -> ADA/USDT
2025-06-26 17:39:10,889 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:39:10,889 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 17:39:10,890 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 17:39:10,890 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-04-04 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-04-09 00:00:00+00:00: BNB/USDT -> BTC/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-04-17 00:00:00+00:00: BTC/USDT -> SOL/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:39:10,891 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> BTC/USDT
2025-06-26 17:39:10,891 - root - INFO - Total trades: 17 (Entries: 1, Exits: 0, Swaps: 16)
2025-06-26 17:39:10,891 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:39:10,891 - root - INFO - DEBUG: self.elapsed_time = 0.11037087440490723 seconds
2025-06-26 17:39:10,891 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:39:10,891 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:39:10,891 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:39:10,891 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:39:10,900 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:39:10,900 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:39:10,900 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:39:10,901 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,901 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,901 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,905 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,907 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,907 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,911 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,911 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,911 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,911 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,911 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,911 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,911 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:39:10,917 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:39:10,917 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:39:10,921 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 17:39:10,921 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 17:39:10,923 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:39:10,924 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:39:10,924 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:39:10,924 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:39:10,924 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:39:10,924 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:39:10,924 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:39:10,931 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:39:10,931 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:39:10,931 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:39:10,934 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:39:10,934 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:39:10,938 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:39:10,938 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:39:10,941 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:39:10,941 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:39:10,941 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:39:10,941 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:39:10,946 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:39:10,946 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:39:10,946 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:39:10,946 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:39:10,950 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:39:10,958 - root - INFO - Configuration loaded successfully.
2025-06-26 17:39:10,966 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:39:11,054 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:39:11,060 - root - INFO - Configuration loaded successfully.
2025-06-26 17:39:12,350 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,350 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,350 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,352 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,353 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,353 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:39:12,353 - root - INFO - Added 13 buy-and-hold curves to results
2025-06-26 17:39:12,353 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,353 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,354 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,354 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,354 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,354 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,354 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,354 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,355 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,355 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,355 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,355 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,355 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:39:12,367 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:39:12,367 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:39:12,370 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:39:12,370 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:39:12,374 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:39:12,375 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:39:12,376 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:39:12,376 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:39:12,376 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 12.0)
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-26 17:39:12,377 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-26 17:39:12,378 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-26 17:39:12,378 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-26 17:39:12,378 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 3.0)
2025-06-26 17:39:12,378 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-26 17:39:12,378 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-26 17:39:12,378 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:39:12,378 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:39:12,379 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:39:12,425 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_173856.csv
2025-06-26 17:39:12,440 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_173856.csv
2025-06-26 17:39:12,440 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:39:12,442 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:39:12,442 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:39:12,442 - root - INFO - Success flag set to: True
2025-06-26 17:39:12,442 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:39:12,442 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:39:12,442 - root - INFO -   - buy_hold_curves: dict with 13 entries
2025-06-26 17:39:12,443 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:39:12,443 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:39:12,443 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:39:12,443 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:39:12,443 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:39:12,443 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:39:12,443 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:39:12,443 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:39:12,444 - root - INFO -   - message: <class 'str'>
2025-06-26 17:39:12,444 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:39:12,444 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 17:39:12,444 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: BTC/EUR
2025-06-26 17:39:12,445 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    BTC/EUR
2025-06-22 00:00:00+00:00    BTC/EUR
2025-06-23 00:00:00+00:00    BTC/EUR
2025-06-24 00:00:00+00:00    BTC/EUR
2025-06-25 00:00:00+00:00    BTC/EUR
dtype: object
2025-06-26 17:39:12,445 - root - ERROR - [DEBUG] ASSET SELECTION - No 'latest_scores' found in results
2025-06-26 17:39:12,469 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR',
       'DOT/EUR'],
      dtype='object')
2025-06-26 17:39:12,469 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:39:12,469 - root - INFO - Single asset strategy with best asset: BTC/EUR
2025-06-26 17:39:12,469 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 17:39:12,469 - root - INFO - [DEBUG]   - Best asset selected: BTC/EUR
2025-06-26 17:39:12,469 - root - INFO - [DEBUG]   - Assets held: {'BTC/EUR': 1.0}
2025-06-26 17:39:12,469 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-26 17:39:12,469 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-26 17:39:12,475 - root - INFO - Executing single-asset strategy with best asset: BTC/EUR
2025-06-26 17:39:12,476 - root - INFO - Executing strategy signal: best_asset=BTC/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:39:12,476 - root - INFO - Incremented daily trade counter for BTC/EUR: 1/5
2025-06-26 17:39:12,476 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: BTC/EUR
2025-06-26 17:39:12,476 - root - INFO - Attempting to enter position for BTC/EUR in paper mode
2025-06-26 17:39:12,476 - root - INFO - [DEBUG] TRADE - BTC/EUR: Starting enter_position attempt
2025-06-26 17:39:12,476 - root - INFO - [DEBUG] TRADE - BTC/EUR: Trading mode: paper
2025-06-26 17:39:12,476 - root - INFO - TRADE ATTEMPT - BTC/EUR: Getting current market price...
2025-06-26 17:39:12,476 - root - INFO - [DEBUG] PRICE - BTC/EUR: Starting get_current_price
2025-06-26 17:39:12,477 - root - INFO - [DEBUG] PRICE - BTC/EUR: Exchange ID: bitvavo
2025-06-26 17:39:12,477 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Initializing exchange bitvavo
2025-06-26 17:39:12,479 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange initialized successfully
2025-06-26 17:39:12,480 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange markets not loaded, loading now...
2025-06-26 17:39:12,719 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Symbol found after loading markets
2025-06-26 17:39:12,719 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Attempting to fetch ticker...
2025-06-26 17:39:12,761 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker fetched successfully
2025-06-26 17:39:12,762 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker data: {'symbol': 'BTC/EUR', 'timestamp': 1750952347252, 'datetime': '2025-06-26T15:39:07.252Z', 'high': 92720.0, 'low': 91111.0, 'bid': 91335.0, 'bidVolume': 0.046752, 'ask': 91341.0, 'askVolume': 2.0, 'vwap': 91985.51690021643, 'open': 92119.0, 'close': 91339.0, 'last': 91339.0, 'previousClose': None, 'change': -780.0, 'percentage': -0.8467308589976009, 'average': 91729.0, 'baseVolume': 341.45684285, 'quoteVolume': 31409084.18867322, 'info': {'market': 'BTC-EUR', 'startTimestamp': 1750865947252, 'timestamp': 1750952347252, 'open': '92119', 'openTimestamp': 1750865962052, 'high': '9.272E+4', 'low': '91111', 'last': '91339', 'closeTimestamp': 1750952340376, 'bid': '91335', 'bidSize': '0.04675200', 'ask': '91341', 'askSize': '2', 'volume': '341.45684285', 'volumeQuote': '31409084.18867322'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 17:39:12,762 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Last price: 91339.0
2025-06-26 17:39:12,762 - root - INFO - [DEBUG] TRADE - BTC/EUR: get_current_price returned: 91339.0
2025-06-26 17:39:12,763 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price type: <class 'float'>
2025-06-26 17:39:12,763 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - not price: False
2025-06-26 17:39:12,763 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - price <= 0: False
2025-06-26 17:39:12,763 - root - INFO - TRADE ATTEMPT - BTC/EUR: Current price: 91339.********
2025-06-26 17:39:12,763 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:39:12,767 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:39:12,768 - root - INFO - Calculated position size for BTC/EUR: 0.******** (using 10% of 100, accounting for fees)
2025-06-26 17:39:12,768 - root - INFO - Calculated position size: 0.******** BTC
2025-06-26 17:39:12,768 - root - INFO - Entering position for BTC/EUR: 0.******** units at 91339.******** (value: 9.******** EUR)
2025-06-26 17:39:12,768 - root - INFO - Executing paper market buy order for BTC/EUR
2025-06-26 17:39:12,769 - root - INFO - Paper trading buy for BTC/EUR: original=0.********, adjusted=0.********, after_fee=0.********
2025-06-26 17:39:12,769 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:39:12,771 - root - INFO - Created paper market buy order: BTC/EUR, amount: 0.********178269961353, price: 91339.0
2025-06-26 17:39:12,771 - root - INFO - Filled amount: 0.******** BTC
2025-06-26 17:39:12,771 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:39:12,771 - root - INFO - Successfully entered position: BTC/EUR, amount: 0.********, price: 91339.********
2025-06-26 17:39:12,779 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91339.********, filled=0.********
2025-06-26 17:39:12,779 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:39:12,779 - root - INFO - TRADE SUCCESS - BTC/EUR: Successfully updated current asset
2025-06-26 17:39:12,787 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91339.********, filled=0.********
2025-06-26 17:39:12,788 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:39:12,788 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:39:12,788 - root - INFO - Trade executed: {'success': True, 'symbol': 'BTC/EUR', 'side': 'buy', 'amount': 0.********484710802615, 'price': 91339.0, 'order': {'id': 'paper-1750952352-BTC/EUR-buy-0.********178269961353', 'symbol': 'BTC/EUR', 'side': 'buy', 'type': 'market', 'amount': 0.********178269961353, 'price': 91339.0, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 0.********178269961353, 'remaining': 0, 'timestamp': 1750952352769, 'datetime': '2025-06-26T17:39:12.769814', 'trades': [], 'average': 91339.0, 'average_price': 91339.0}, 'filled_amount': 0.********178269961353, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'BTC', 'timestamp': '2025-06-26T17:39:12.771315'}
2025-06-26 17:39:12,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:39:12,863 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:39:12,863 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:39:12,863 - root - INFO -   BTC/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 17:39:12,863 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,863 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,864 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,864 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,864 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,864 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,865 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,865 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,865 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,865 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,866 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,866 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:39:12,866 - root - INFO - Asset selection logged with 13 assets scored and 1 assets selected
2025-06-26 17:39:12,866 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:39:12,866 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:39:12,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:39:12,903 - root - INFO - Strategy execution completed successfully in 16.80 seconds
2025-06-26 17:39:12,906 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:39:15,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:39:25,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:39:35,744 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:39:45,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:39:55,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:40:05,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:40:15,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:40:25,843 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:40:35,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:40:45,873 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:40:55,890 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:41:05,907 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:41:15,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:41:25,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:41:35,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:41:45,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:41:55,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:42:05,993 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:42:16,007 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:42:26,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:42:36,036 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:42:46,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:42:56,062 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:43:06,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:43:16,091 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:43:26,106 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
